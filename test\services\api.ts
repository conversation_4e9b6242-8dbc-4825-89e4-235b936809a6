// API Service for BAAM Backend Integration
import { Task, Goal, User, NewTaskForm } from '../types';

const API_BASE_URL = 'http://localhost:5001';
const TEST_USER_ID = 'demo_user_123';

// Headers for API requests
const getHeaders = () => ({
  'Content-Type': 'application/json',
  'X-Test-Auth-Uid': TEST_USER_ID, // For development testing
});

// Generic API request function
async function apiRequest<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
  const url = `${API_BASE_URL}${endpoint}`;
  
  const response = await fetch(url, {
    ...options,
    headers: {
      ...getHeaders(),
      ...options.headers,
    },
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}));
    throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
  }

  return response.json();
}

// Task API functions
export const taskAPI = {
  // Get all tasks for the current user
  async getTasks(): Promise<Task[]> {
    return apiRequest<Task[]>(`/api/users/${TEST_USER_ID}/tasks`);
  },

  // Get a specific task
  async getTask(taskId: string): Promise<Task> {
    return apiRequest<Task>(`/api/tasks/${taskId}`);
  },

  // Create a new task
  async createTask(taskData: NewTaskForm): Promise<{ task_id: string; message: string }> {
    // Convert frontend format to backend format
    const backendTask = {
      uid: TEST_USER_ID,
      title: taskData.title,
      description: taskData.notes || '',
      status: 'planned',
      estimated_duration: taskData.startTime && taskData.endTime 
        ? calculateDuration(taskData.startTime, taskData.endTime) 
        : 60, // Default 1 hour
      flexibility: taskData.isFlexible ? 8 : 2, // High flexibility if flexible, low if not
      categories: ['user-created'],
      scheduled_time: taskData.startTime ? formatScheduledTime(taskData.startTime) : undefined,
      is_recurring: taskData.isRecurring,
    };

    return apiRequest<{ task_id: string; message: string }>('/api/tasks', {
      method: 'POST',
      body: JSON.stringify(backendTask),
    });
  },

  // Update a task
  async updateTask(taskId: string, updates: Partial<Task>): Promise<{ message: string }> {
    const backendUpdates = {
      title: updates.title,
      description: updates.notes,
      status: updates.completed ? 'completed' : 'planned',
    };

    return apiRequest<{ message: string }>(`/api/tasks/${taskId}`, {
      method: 'PUT',
      body: JSON.stringify(backendUpdates),
    });
  },

  // Delete a task
  async deleteTask(taskId: string): Promise<{ message: string }> {
    return apiRequest<{ message: string }>(`/api/tasks/${taskId}`, {
      method: 'DELETE',
    });
  },

  // Schedule a task using AI
  async scheduleTask(taskId: string, preferredTime?: string): Promise<any> {
    const scheduleData = preferredTime ? { preferred_time: preferredTime } : {};
    
    return apiRequest(`/api/tasks/${taskId}/schedule`, {
      method: 'POST',
      body: JSON.stringify(scheduleData),
    });
  },
};

// Goal API functions
export const goalAPI = {
  // Get all goals for the current user
  async getGoals(): Promise<Goal[]> {
    return apiRequest<Goal[]>(`/api/users/${TEST_USER_ID}/goals`);
  },

  // Create a new goal
  async createGoal(goalData: Partial<Goal>): Promise<{ goal_id: string; message: string }> {
    const backendGoal = {
      uid: TEST_USER_ID,
      title: goalData.title,
      description: goalData.description || '',
      status: 'active',
      target_date: goalData.target_date,
      categories: goalData.categories || ['user-created'],
    };

    return apiRequest<{ goal_id: string; message: string }>('/api/goals', {
      method: 'POST',
      body: JSON.stringify(backendGoal),
    });
  },
};

// User API functions
export const userAPI = {
  // Get current user
  async getUser(): Promise<User> {
    return apiRequest<User>(`/api/users/${TEST_USER_ID}`);
  },

  // Get user's daily schedule
  async getDailySchedule(date?: string): Promise<any> {
    const dateParam = date || new Date().toISOString().split('T')[0];
    return apiRequest(`/api/users/${TEST_USER_ID}/schedule/daily?date=${dateParam}`);
  },

  // Get productivity insights
  async getInsights(): Promise<any> {
    return apiRequest(`/api/users/${TEST_USER_ID}/insights`);
  },
};

// AI Chat API
export const chatAPI = {
  async sendMessage(message: string, sessionId?: string): Promise<any> {
    return apiRequest('/api/chat', {
      method: 'POST',
      body: JSON.stringify({
        message,
        session_id: sessionId || 'default',
      }),
    });
  },
};

// Utility functions
function calculateDuration(startTime: string, endTime: string): number {
  // Simple duration calculation in minutes
  // This is a basic implementation - you might want to make it more robust
  const [startHour, startMin] = startTime.split(':').map(Number);
  const [endHour, endMin] = endTime.split(':').map(Number);
  
  const startMinutes = startHour * 60 + startMin;
  const endMinutes = endHour * 60 + endMin;
  
  return Math.max(endMinutes - startMinutes, 15); // Minimum 15 minutes
}

function formatScheduledTime(time: string): string {
  // Convert time like "10:00" to ISO format for today
  const today = new Date().toISOString().split('T')[0];
  return `${today}T${time}:00`;
}

// Export all APIs
export const api = {
  tasks: taskAPI,
  goals: goalAPI,
  user: userAPI,
  chat: chatAPI,
};
