import React, { useState } from 'react';
import { ScrollView, Pressable, Alert, Text } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

// Types and Components
import { User, Friend, GameStats, ViewType, NewTaskForm } from '../types';
import {
  <PERSON><PERSON>,
  <PERSON>Toggle,
  TaskList,
  GameSummary,
  FriendsList,
  AddTaskModal,
} from '../components';
import { commonStyles } from '../styles';
import { useTasks } from '../hooks/useTasks';

export default function Index() {
  const [currentView, setCurrentView] = useState<ViewType>('today');
  const [showAddTaskModal, setShowAddTaskModal] = useState(false);

  // Use API for task management
  const { createTask, loading: taskLoading, error: taskError } = useTasks();

  // Demo user data - in a real app, this would come from authentication
  const [user] = useState({
    name: 'Player',
    avatar: '👤',
    rank: 5,
    coins: 320,
    chips: 5,
  });

  const [gameStats] = useState<GameStats>({
    coins: 320,
    chips: 5,
    cardEffects: ['+25% Daily Bonus', 'Streak Multiplier x2'],
    availablePacks: ['Epic Pack', 'Rare Pack'],
  });

  const [friends] = useState<Friend[]>([
    { id: '1', name: 'Bob', rank: 3, avatar: '👨' },
    { id: '2', name: 'Alice', rank: 7, avatar: '👩' },
  ]);

  // New task form state
  const [newTask, setNewTask] = useState<NewTaskForm>({
    title: '',
    startTime: '',
    endTime: '',
    isRecurring: false,
    isFlexible: true,
    notes: '',
  });

  // Event handlers
  const handleAddTask = async () => {
    if (!newTask.title.trim()) {
      Alert.alert('Error', 'Please enter a task title');
      return;
    }

    try {
      await createTask(newTask);

      // Reset form and close modal
      setNewTask({
        title: '',
        startTime: '',
        endTime: '',
        isRecurring: false,
        isFlexible: true,
        notes: '',
      });
      setShowAddTaskModal(false);

      // TODO: Award coins for task creation
      Alert.alert('Success', 'Task created successfully!');
    } catch (error) {
      Alert.alert('Error', 'Failed to create task. Please try again.');
    }
  };

  const handleUpdateNewTask = (updates: Partial<NewTaskForm>) => {
    setNewTask({ ...newTask, ...updates });
  };

  const handleSettingsPress = () => {
    // TODO: Navigate to settings screen
    Alert.alert('Settings', 'Settings screen coming soon!');
  };

  const handleTradeCards = (friendId: string) => {
    // TODO: Navigate to card trading screen
    Alert.alert('Trade Cards', `Trading cards with friend ${friendId}`);
  };

  return (
    <SafeAreaView style={commonStyles.container}>
      <ScrollView style={commonStyles.scrollView}>
        <Header user={user as any} onSettingsPress={handleSettingsPress} />

        <ViewToggle currentView={currentView} onViewChange={setCurrentView} />

        <TaskList />

        <Pressable
          style={[commonStyles.button, taskLoading && { opacity: 0.6 }]}
          onPress={() => setShowAddTaskModal(true)}
          disabled={taskLoading}
        >
          <Text style={commonStyles.buttonText}>
            {taskLoading ? '⏳ Creating...' : '➕ Add Task'}
          </Text>
        </Pressable>

        <GameSummary gameStats={gameStats} />

        <FriendsList friends={friends} onTradeCards={handleTradeCards} />
      </ScrollView>

      <AddTaskModal
        visible={showAddTaskModal}
        newTask={newTask}
        onClose={() => setShowAddTaskModal(false)}
        onSave={handleAddTask}
        onUpdateTask={handleUpdateNewTask}
      />
    </SafeAreaView>
  );
}


