import React, { useState } from 'react';
import {
  View,
  Text,
  ScrollView,
  Pressable,
  StyleSheet,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { TaskList } from './TaskList';
import { AddTaskModal } from './AddTaskModal';
import { NewTaskForm } from '../types';

export const ApiIntegrationExample: React.FC = () => {
  const [showAddModal, setShowAddModal] = useState(false);
  const [newTask, setNewTask] = useState<NewTaskForm>({
    title: '',
    startTime: '',
    endTime: '',
    isRecurring: false,
    isFlexible: false,
    notes: '',
  });

  const resetNewTask = () => {
    setNewTask({
      title: '',
      startTime: '',
      endTime: '',
      isRecurring: false,
      isFlexible: false,
      notes: '',
    });
  };

  const handleAddTask = () => {
    setShowAddModal(true);
  };

  const handleCloseModal = () => {
    setShowAddModal(false);
    resetNewTask();
  };

  const handleTaskCreated = () => {
    // Task was successfully created, modal will close automatically
    resetNewTask();
  };

  const updateNewTask = (updates: Partial<NewTaskForm>) => {
    setNewTask(prev => ({ ...prev, ...updates }));
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title}>🚀 BAAM API Integration</Text>
          <Text style={styles.subtitle}>
            Your tasks are now connected to the backend!
          </Text>
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Tasks</Text>
            <Pressable style={styles.addButton} onPress={handleAddTask}>
              <Text style={styles.addButtonText}>+ Add Task</Text>
            </Pressable>
          </View>
          
          {/* TaskList will automatically load tasks from the API */}
          <TaskList />
        </View>

        <View style={styles.infoSection}>
          <Text style={styles.infoTitle}>🎯 What's Working:</Text>
          <Text style={styles.infoText}>
            • ✅ Tasks load from your Flask API{'\n'}
            • ✅ Create new tasks with the API{'\n'}
            • ✅ Mark tasks as complete/incomplete{'\n'}
            • ✅ Delete tasks{'\n'}
            • ✅ Real-time updates{'\n'}
            • ✅ Error handling & loading states
          </Text>
        </View>

        <View style={styles.infoSection}>
          <Text style={styles.infoTitle}>🔧 API Endpoints Used:</Text>
          <Text style={styles.infoText}>
            • GET /api/users/demo_user_123/tasks{'\n'}
            • POST /api/tasks{'\n'}
            • PUT /api/tasks/:id{'\n'}
            • DELETE /api/tasks/:id
          </Text>
        </View>
      </ScrollView>

      <AddTaskModal
        visible={showAddModal}
        newTask={newTask}
        onClose={handleCloseModal}
        onUpdateTask={updateNewTask}
        onTaskCreated={handleTaskCreated}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
  },
  header: {
    padding: 20,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666',
  },
  section: {
    backgroundColor: '#fff',
    marginTop: 10,
    padding: 20,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
  },
  addButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 8,
  },
  addButtonText: {
    color: '#fff',
    fontWeight: '600',
  },
  infoSection: {
    backgroundColor: '#fff',
    marginTop: 10,
    padding: 20,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 20,
  },
});
