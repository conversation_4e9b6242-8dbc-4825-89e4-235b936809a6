// Types for the Todo List App

export interface Task {
  task_id: string;
  uid: string;
  title: string;
  description?: string;
  status: 'planned' | 'in_progress' | 'completed' | 'cancelled';
  estimated_duration: number; // in minutes
  flexibility: number; // 1-10 scale
  categories: string[];
  scheduled_time?: string; // ISO format
  due_date?: string; // ISO format
  created_at: string;
  updated_at: string;
  completed_at?: string;
  is_recurring?: boolean;
  // Frontend-specific fields for compatibility
  id?: string; // alias for task_id
  completed?: boolean; // computed from status
  startTime?: string; // computed from scheduled_time
  endTime?: string; // computed from scheduled_time + duration
  isRecurring?: boolean; // alias for is_recurring
  isFlexible?: boolean; // computed from flexibility
  notes?: string; // alias for description
}

export interface Goal {
  goal_id: string;
  uid: string;
  title: string;
  description?: string;
  status: 'active' | 'completed' | 'paused' | 'cancelled';
  target_date?: string; // ISO format
  categories: string[];
  created_at: string;
  updated_at: string;
  completed_at?: string;
}

export interface User {
  uid: string;
  email: string;
  display_name: string;
  created_at: string;
  updated_at: string;
  preferences?: {
    timezone?: string;
    work_hours?: {
      start: string;
      end: string;
    };
    notification_settings?: any;
  };
  // Frontend-specific fields for compatibility
  name?: string; // alias for display_name
  avatar?: string;
  rank?: number;
  coins?: number;
  chips?: number;
}

export interface Friend {
  id: string;
  name: string;
  rank: number;
  avatar: string;
}

export interface GameStats {
  coins: number;
  chips: number;
  cardEffects: string[];
  availablePacks: string[];
}

export type ViewType = 'today' | 'calendar';

export interface NewTaskForm {
  title: string;
  startTime: string;
  endTime: string;
  isRecurring: boolean;
  isFlexible: boolean;
  notes: string;
}
