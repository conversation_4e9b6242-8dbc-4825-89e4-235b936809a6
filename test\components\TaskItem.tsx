import React from 'react';
import { View, Text, Pressable, StyleSheet } from 'react-native';
import { Task } from '../types';

interface TaskItemProps {
  task: Task;
  onToggle: (taskId: string) => void;
  onDelete?: (taskId: string) => void;
}

export const TaskItem: React.FC<TaskItemProps> = ({ task, onToggle, onDelete }) => {
  // Get task ID (support both old and new format)
  const taskId = task.task_id || task.id || '';

  // Get completion status (support both old and new format)
  const isCompleted = task.completed || task.status === 'completed';

  const getTaskTimeDisplay = (task: Task) => {
    // Handle scheduled time from API
    if (task.scheduled_time) {
      const scheduledDate = new Date(task.scheduled_time);
      const timeStr = scheduledDate.toLocaleTimeString('en-US', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit'
      });
      return `⏰ ${timeStr}`;
    }

    // Handle legacy format
    if (task.startTime && task.endTime) {
      return `⏰ ${task.startTime} – ${task.endTime}`;
    }

    // Handle recurring tasks
    if (task.isRecurring || task.is_recurring) {
      return '🔁 Daily';
    }

    // Handle flexible tasks
    if (task.isFlexible || (task.flexibility && task.flexibility > 5)) {
      return '⏳ Flexible';
    }

    // Show estimated duration if available
    if (task.estimated_duration) {
      return `⏱️ ${task.estimated_duration}min`;
    }

    return '';
  };

  const getTaskDescription = (task: Task) => {
    return task.description || task.notes || '';
  };

  return (
    <View style={styles.taskItem}>
      <Pressable
        style={styles.taskCheckboxContainer}
        onPress={() => onToggle(taskId)}
      >
        <Text style={styles.taskCheckbox}>
          {isCompleted ? '✅' : '☐'}
        </Text>
      </Pressable>

      <View style={styles.taskContent}>
        <Text style={[styles.taskTitle, isCompleted && styles.completedTask]}>
          {task.title}
        </Text>

        {getTaskDescription(task) && (
          <Text style={styles.taskDescription}>
            {getTaskDescription(task)}
          </Text>
        )}

        <Text style={styles.taskTime}>
          {getTaskTimeDisplay(task)}
        </Text>

        {task.categories && task.categories.length > 0 && (
          <Text style={styles.taskCategories}>
            🏷️ {task.categories.join(', ')}
          </Text>
        )}
      </View>

      {onDelete && (
        <Pressable
          style={styles.deleteButton}
          onPress={() => onDelete(taskId)}
        >
          <Text style={styles.deleteButtonText}>🗑️</Text>
        </Pressable>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  taskItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
    backgroundColor: '#fff',
  },
  taskCheckboxContainer: {
    paddingTop: 2,
  },
  taskCheckbox: {
    fontSize: 20,
    marginRight: 12,
  },
  taskContent: {
    flex: 1,
  },
  taskTitle: {
    fontSize: 16,
    color: '#333',
    marginBottom: 4,
    fontWeight: '500',
  },
  completedTask: {
    textDecorationLine: 'line-through',
    color: '#999',
  },
  taskDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 4,
    lineHeight: 18,
  },
  taskTime: {
    fontSize: 12,
    color: '#888',
    marginBottom: 2,
  },
  taskCategories: {
    fontSize: 12,
    color: '#007AFF',
    fontStyle: 'italic',
  },
  deleteButton: {
    padding: 8,
    marginLeft: 8,
  },
  deleteButtonText: {
    fontSize: 16,
    color: '#FF3B30',
  },
});
